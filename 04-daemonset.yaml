---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: fluent-bit
  namespace: fluent-bit
  labels:
    app.kubernetes.io/name: fluent-bit
    app.kubernetes.io/component: logging
    app.kubernetes.io/version: "2.2.0"
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: fluent-bit
  template:
    metadata:
      labels:
        app.kubernetes.io/name: fluent-bit
        app.kubernetes.io/component: logging
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "2020"
        prometheus.io/path: /api/v1/metrics/prometheus
    spec:
      serviceAccountName: fluent-bit
      hostNetwork: false
      dnsPolicy: ClusterFirst
      
      # Security context for the pod
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
        seccompProfile:
          type: RuntimeDefault
      
      # Node selection and tolerations
      nodeSelector:
        kubernetes.io/os: linux
      
      tolerations:
        - key: node-role.kubernetes.io/master
          operator: Exists
          effect: NoSchedule
        - key: node-role.kubernetes.io/control-plane
          operator: Exists
          effect: NoSchedule
        - operator: "Exists"
          effect: "NoExecute"
        - operator: "Exists"
          effect: "NoSchedule"
      
      # Init container to set up permissions
      initContainers:
        - name: init-fluent-bit
          image: busybox:1.35
          command:
            - /bin/sh
            - -c
            - |
              mkdir -p /fluent-bit/db /fluent-bit/buffer /fluent-bit/config-backup
              chown -R 1000:1000 /fluent-bit
              chmod -R 755 /fluent-bit
          securityContext:
            runAsUser: 0
            runAsGroup: 0
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - ALL
              add:
                - CHOWN
                - FOWNER
          volumeMounts:
            - name: fluent-bit-db
              mountPath: /fluent-bit/db
            - name: fluent-bit-buffer
              mountPath: /fluent-bit/buffer
            - name: fluent-bit-config-backup
              mountPath: /fluent-bit/config-backup
      
      containers:
        - name: fluent-bit
          image: fluent/fluent-bit:2.2.0
          imagePullPolicy: IfNotPresent
          
          # Security context for the container
          securityContext:
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: true
            runAsNonRoot: true
            runAsUser: 1000
            runAsGroup: 1000
            capabilities:
              drop:
                - ALL
          
          # Resource limits and requests
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 128Mi
          
          # Environment variables
          env:
            - name: FLUENT_CONF
              value: /fluent-bit/etc/fluent-bit.conf
            - name: NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: CLUSTER_NAME
              value: "aks-cluster"  # Replace with your actual cluster name
          
          # Ports
          ports:
            - name: http
              containerPort: 2020
              protocol: TCP
          
          # Health checks
          livenessProbe:
            httpGet:
              path: /
              port: http
            initialDelaySeconds: 30
            periodSeconds: 30
            timeoutSeconds: 5
            failureThreshold: 3
          
          readinessProbe:
            httpGet:
              path: /api/v1/health
              port: http
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          
          # Volume mounts
          volumeMounts:
            # Configuration
            - name: config
              mountPath: /fluent-bit/etc
              readOnly: true
            
            # Persistent storage volumes
            - name: fluent-bit-db
              mountPath: /fluent-bit/db
            - name: fluent-bit-buffer
              mountPath: /fluent-bit/buffer
            - name: fluent-bit-config-backup
              mountPath: /fluent-bit/config-backup
            
            # Host log directories (read-only)
            - name: varlog
              mountPath: /var/log
              readOnly: true
            - name: varlibdockercontainers
              mountPath: /var/lib/docker/containers
              readOnly: true
            
            # Kubernetes service account token
            - name: kube-api-access
              mountPath: /var/run/secrets/kubernetes.io/serviceaccount
              readOnly: true
            
            # Temporary directories
            - name: tmp
              mountPath: /tmp
            - name: var-tmp
              mountPath: /var/tmp
      
      # Volumes
      volumes:
        # ConfigMap volume
        - name: config
          configMap:
            name: fluent-bit-config
        
        # Persistent volumes
        - name: fluent-bit-db
          persistentVolumeClaim:
            claimName: fluent-bit-db-pvc
        - name: fluent-bit-buffer
          persistentVolumeClaim:
            claimName: fluent-bit-buffer-pvc
        - name: fluent-bit-config-backup
          persistentVolumeClaim:
            claimName: fluent-bit-config-pvc
        
        # Host volumes (read-only)
        - name: varlog
          hostPath:
            path: /var/log
        - name: varlibdockercontainers
          hostPath:
            path: /var/lib/docker/containers
        
        # Service account token
        - name: kube-api-access
          projected:
            sources:
              - serviceAccountToken:
                  expirationSeconds: 3607
                  path: token
              - configMap:
                  items:
                    - key: ca.crt
                      path: ca.crt
                  name: kube-root-ca.crt
              - downwardAPI:
                  items:
                    - fieldRef:
                        apiVersion: v1
                        fieldPath: metadata.namespace
                      path: namespace
        
        # Temporary volumes
        - name: tmp
          emptyDir: {}
        - name: var-tmp
          emptyDir: {}
  
  # Update strategy
  updateStrategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
