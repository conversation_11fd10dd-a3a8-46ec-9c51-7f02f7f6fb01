---
# Namespace for Fluent Bit
apiVersion: v1
kind: Namespace
metadata:
  name: fluent-bit
  labels:
    name: fluent-bit
    app.kubernetes.io/name: fluent-bit
    app.kubernetes.io/component: logging

---
# Service Account for Fluent Bit
apiVersion: v1
kind: ServiceAccount
metadata:
  name: fluent-bit
  namespace: fluent-bit
  labels:
    app.kubernetes.io/name: fluent-bit
    app.kubernetes.io/component: logging

---
# ClusterRole for Fluent Bit
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: fluent-bit
  labels:
    app.kubernetes.io/name: fluent-bit
    app.kubernetes.io/component: logging
rules:
  # Allow reading pods and namespaces for log collection
  - apiGroups: [""]
    resources:
      - namespaces
      - pods
      - pods/logs
      - nodes
      - nodes/proxy
    verbs: ["get", "list", "watch"]
  
  # Allow reading endpoints for service discovery
  - apiGroups: [""]
    resources:
      - endpoints
      - services
    verbs: ["get", "list", "watch"]
  
  # Allow reading configmaps for configuration updates
  - apiGroups: [""]
    resources:
      - configmaps
    verbs: ["get", "list", "watch"]
  
  # Allow reading secrets for authentication
  - apiGroups: [""]
    resources:
      - secrets
    verbs: ["get", "list", "watch"]
  
  # Allow reading events for monitoring
  - apiGroups: [""]
    resources:
      - events
    verbs: ["get", "list", "watch"]
  
  # Allow reading persistent volumes and claims
  - apiGroups: [""]
    resources:
      - persistentvolumes
      - persistentvolumeclaims
    verbs: ["get", "list", "watch"]

---
# ClusterRoleBinding for Fluent Bit
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: fluent-bit
  labels:
    app.kubernetes.io/name: fluent-bit
    app.kubernetes.io/component: logging
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: fluent-bit
subjects:
  - kind: ServiceAccount
    name: fluent-bit
    namespace: fluent-bit
