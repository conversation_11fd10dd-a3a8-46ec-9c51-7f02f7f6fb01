---
apiVersion: v1
kind: ConfigMap
metadata:
  name: fluent-bit-config
  namespace: fluent-bit
  labels:
    app.kubernetes.io/name: fluent-bit
    app.kubernetes.io/component: logging
data:
  # Main Fluent Bit configuration
  fluent-bit.conf: |
    [SERVICE]
        Daemon Off
        Flush 1
        Log_Level info
        Parsers_File parsers.conf
        Plugins_File plugins.conf
        HTTP_Server On
        HTTP_Listen 0.0.0.0
        HTTP_Port 2020
        Health_Check On
        # Use persistent storage for database
        storage.path /fluent-bit/db/
        storage.sync normal
        storage.checksum off
        storage.backlog.mem_limit 50M
        storage.metrics on

    [INPUT]
        Name tail
        Path /var/log/containers/*.log
        multiline.parser docker, cri
        Tag kube.*
        Mem_Buf_Limit 50MB
        Skip_Long_Lines On
        Skip_Empty_Lines On
        # Use persistent buffer storage
        storage.type filesystem
        Buffer_Chunk_Size 32k
        Buffer_Max_Size 256k
        Refresh_Interval 10

    [INPUT]
        Name systemd
        Tag host.*
        Systemd_Filter _SYSTEMD_UNIT=kubelet.service
        Systemd_Filter _SYSTEMD_UNIT=docker.service
        Systemd_Filter _SYSTEMD_UNIT=containerd.service
        Read_From_Tail On
        # Use persistent buffer storage
        storage.type filesystem

    [FILTER]
        Name kubernetes
        Match kube.*
        Kube_URL https://kubernetes.default.svc:443
        Kube_CA_File /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        Kube_Token_File /var/run/secrets/kubernetes.io/serviceaccount/token
        Kube_Tag_Prefix kube.var.log.containers.
        Merge_Log On
        Merge_Log_Key log_processed
        K8S-Logging.Parser On
        K8S-Logging.Exclude Off
        Annotations Off
        Labels On

    [FILTER]
        Name nest
        Match kube.*
        Operation lift
        Nested_under kubernetes
        Add_prefix kubernetes_

    [FILTER]
        Name modify
        Match kube.*
        Add cluster_name ${CLUSTER_NAME}
        Add node_name ${NODE_NAME}

    [OUTPUT]
        Name stdout
        Match *
        Format json_lines

    # Example Azure Log Analytics output (uncomment and configure as needed)
    # [OUTPUT]
    #     Name azure
    #     Match kube.*
    #     Customer_ID ${AZURE_LOG_ANALYTICS_WORKSPACE_ID}
    #     Shared_Key ${AZURE_LOG_ANALYTICS_SHARED_KEY}
    #     Log_Type FluentBitLogs

  # Parser configuration
  parsers.conf: |
    [PARSER]
        Name docker
        Format json
        Time_Key time
        Time_Format %Y-%m-%dT%H:%M:%S.%L
        Time_Keep On

    [PARSER]
        Name cri
        Format regex
        Regex ^(?<time>[^ ]+) (?<stream>stdout|stderr) (?<logtag>[^ ]*) (?<message>.*)$
        Time_Key time
        Time_Format %Y-%m-%dT%H:%M:%S.%L%z

    [PARSER]
        Name syslog
        Format regex
        Regex ^\<(?<pri>[0-9]+)\>(?<time>[^ ]* {1,2}[^ ]* [^ ]*) (?<host>[^ ]*) (?<ident>[a-zA-Z0-9_\/\.\-]*)(?:\[(?<pid>[0-9]+)\])?(?:[^\:]*\:)? *(?<message>.*)$
        Time_Key time
        Time_Format %b %d %H:%M:%S

  # Plugins configuration
  plugins.conf: |
    [PLUGINS]
        Path /fluent-bit/bin/out_azure.so
