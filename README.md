# Fluent Bit DaemonSet for Azure Kubernetes Service (AKS)

This repository contains Kubernetes manifests and deployment scripts for running Fluent Bit as a DaemonSet on Azure Kubernetes Service (AKS) with persistent storage using Persistent Volume Claims (PVC).

## Overview

This deployment provides:
- **Fluent Bit DaemonSet** running on all nodes in your AKS cluster
- **Persistent storage** for logs, buffers, and configuration data
- **RBAC permissions** with least-privilege access
- **Security hardening** with non-root containers and read-only filesystems
- **Resource limits** and health checks
- **Azure-optimized storage classes** for Premium SSD performance

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                        AKS Cluster                         │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │    Node 1   │  │    Node 2   │  │    Node N   │        │
│  │             │  │             │  │             │        │
│  │ ┌─────────┐ │  │ ┌─────────┐ │  │ ┌─────────┐ │        │
│  │ │Fluent   │ │  │ │Fluent   │ │  │ │Fluent   │ │        │
│  │ │Bit Pod  │ │  │ │Bit Pod  │ │  │ │Bit Pod  │ │        │
│  │ └─────────┘ │  │ └─────────┘ │  │ └─────────┘ │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
├─────────────────────────────────────────────────────────────┤
│                   Persistent Storage                       │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │Buffer PVC   │  │Config PVC   │  │Database PVC │        │
│  │(10Gi)       │  │(1Gi)        │  │(2Gi)        │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
```

## Files Description

| File | Description |
|------|-------------|
| `01-namespace-rbac.yaml` | Namespace, ServiceAccount, ClusterRole, and ClusterRoleBinding |
| `02-storage.yaml` | StorageClass and PersistentVolumeClaim configurations |
| `03-configmap.yaml` | Fluent Bit configuration with persistent storage settings |
| `04-daemonset.yaml` | DaemonSet with security contexts and volume mounts |
| `deploy.sh` | Deployment script with status checking and cleanup |
| `README.md` | This documentation file |

## Prerequisites

1. **Azure Kubernetes Service (AKS) cluster** with at least one node
2. **kubectl** configured to connect to your AKS cluster
3. **Cluster admin permissions** to create ClusterRole and ClusterRoleBinding
4. **Azure Premium SSD storage** available in your cluster

### Verify Prerequisites

```bash
# Check kubectl connection
kubectl cluster-info

# Check available storage classes
kubectl get storageclass

# Check node readiness
kubectl get nodes
```

## Quick Start

1. **Clone or download** the manifests to your local machine

2. **Make the deployment script executable:**
   ```bash
   chmod +x deploy.sh
   ```

3. **Deploy Fluent Bit:**
   ```bash
   ./deploy.sh deploy
   ```

4. **Check deployment status:**
   ```bash
   ./deploy.sh status
   ```

## Manual Deployment

If you prefer to deploy manually:

```bash
# Apply manifests in order
kubectl apply -f 01-namespace-rbac.yaml
kubectl apply -f 02-storage.yaml
kubectl apply -f 03-configmap.yaml
kubectl apply -f 04-daemonset.yaml

# Wait for deployment
kubectl rollout status daemonset/fluent-bit -n fluent-bit

# Check status
kubectl get pods -n fluent-bit -o wide
```

## Configuration

### Storage Configuration

The deployment creates three PVCs:

- **Buffer PVC** (10Gi): For log buffering and temporary storage
- **Config PVC** (1Gi): For configuration backups
- **Database PVC** (2Gi): For Fluent Bit's internal database

### Cluster Name

Update the cluster name in the DaemonSet:

```yaml
env:
  - name: CLUSTER_NAME
    value: "your-aks-cluster-name"  # Update this
```

### Output Configuration

The default configuration outputs to stdout. To configure Azure Log Analytics:

1. Uncomment the Azure output section in `03-configmap.yaml`
2. Set your workspace ID and shared key as environment variables or secrets

## Monitoring and Troubleshooting

### Check Pod Status

```bash
kubectl get pods -n fluent-bit -o wide
```

### View Logs

```bash
# View logs from all pods
kubectl logs -n fluent-bit -l app.kubernetes.io/name=fluent-bit

# View logs from specific pod
kubectl logs -n fluent-bit <pod-name>

# Follow logs
kubectl logs -n fluent-bit -l app.kubernetes.io/name=fluent-bit -f
```

### Check Persistent Volumes

```bash
# Check PVC status
kubectl get pvc -n fluent-bit

# Check PV details
kubectl get pv
```

### Health Checks

Fluent Bit exposes health endpoints:

```bash
# Port forward to access health endpoint
kubectl port-forward -n fluent-bit <pod-name> 2020:2020

# Check health (in another terminal)
curl http://localhost:2020/api/v1/health
```

## Security Features

- **Non-root containers** with user ID 1000
- **Read-only root filesystem** for enhanced security
- **Dropped capabilities** with minimal required permissions
- **Security contexts** applied at pod and container level
- **Network policies** ready (can be added as needed)

## Resource Management

Default resource limits:
- **CPU**: 500m limit, 100m request
- **Memory**: 512Mi limit, 128Mi request

Adjust based on your cluster's log volume and performance requirements.

## Cleanup

To remove the Fluent Bit deployment:

```bash
./deploy.sh cleanup
```

Or manually:

```bash
kubectl delete -f 04-daemonset.yaml
kubectl delete -f 03-configmap.yaml
kubectl delete -f 02-storage.yaml
kubectl delete -f 01-namespace-rbac.yaml
```

## Customization

### Adding Custom Parsers

Edit `03-configmap.yaml` and add your parsers to the `parsers.conf` section.

### Modifying Storage Size

Edit `02-storage.yaml` and adjust the storage requests in the PVC specifications.

### Adding Outputs

Edit `03-configmap.yaml` and add additional output configurations as needed.

## Support

For issues related to:
- **Fluent Bit configuration**: Check the [official documentation](https://docs.fluentbit.io/)
- **AKS-specific issues**: Consult [Azure AKS documentation](https://docs.microsoft.com/en-us/azure/aks/)
- **Kubernetes concepts**: Refer to [Kubernetes documentation](https://kubernetes.io/docs/)

## License

This configuration is provided as-is for educational and production use. Please review and test thoroughly before deploying to production environments.
