---
# StorageClass for Azure Disk (Premium SSD)
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: fluent-bit-storage
  labels:
    app.kubernetes.io/name: fluent-bit
    app.kubernetes.io/component: logging
provisioner: disk.csi.azure.com
parameters:
  # Use Premium SSD for better performance
  skuName: Premium_LRS
  # Enable encryption at rest
  encryption: true
  # Set caching mode for better performance
  cachingmode: ReadOnly
  # Set disk type
  kind: Managed
reclaimPolicy: Retain
allowVolumeExpansion: true
volumeBindingMode: WaitForFirstConsumer

---
# Alternative StorageClass for Azure Files (for shared storage if needed)
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: fluent-bit-files-storage
  labels:
    app.kubernetes.io/name: fluent-bit
    app.kubernetes.io/component: logging
provisioner: file.csi.azure.com
parameters:
  # Use Premium tier for better performance
  skuName: Premium_LRS
  # Enable encryption
  encryption: true
  # Set protocol
  protocol: nfs
reclaimPolicy: Retain
allowVolumeExpansion: true
volumeBindingMode: Immediate

---
# PersistentVolumeClaim for Fluent Bit buffer storage
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: fluent-bit-buffer-pvc
  namespace: fluent-bit
  labels:
    app.kubernetes.io/name: fluent-bit
    app.kubernetes.io/component: logging
    fluent-bit.io/storage-type: buffer
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: fluent-bit-storage
  resources:
    requests:
      storage: 10Gi
  # Optional: Add selector if you need specific volume characteristics
  # selector:
  #   matchLabels:
  #     environment: production

---
# PersistentVolumeClaim for Fluent Bit configuration backup
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: fluent-bit-config-pvc
  namespace: fluent-bit
  labels:
    app.kubernetes.io/name: fluent-bit
    app.kubernetes.io/component: logging
    fluent-bit.io/storage-type: config
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: fluent-bit-storage
  resources:
    requests:
      storage: 1Gi

---
# PersistentVolumeClaim for Fluent Bit database/state storage
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: fluent-bit-db-pvc
  namespace: fluent-bit
  labels:
    app.kubernetes.io/name: fluent-bit
    app.kubernetes.io/component: logging
    fluent-bit.io/storage-type: database
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: fluent-bit-storage
  resources:
    requests:
      storage: 2Gi
